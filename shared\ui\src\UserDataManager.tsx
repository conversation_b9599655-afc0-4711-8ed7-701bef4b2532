'use client';

import React, { useState, useCallback } from 'react';
import { Button } from './Button';
import { UserConsent, JournalEntry } from '@metiscore/types';

interface UserDataExport {
  export_info: {
    timestamp: string;
    user_id: string;
    export_type: 'full_data_export';
    jurisdiction: string;
    version: string;
  };
  personal_information: {
    user_id: string;
    email: string;
    display_name: string;
    account_created: string;
    last_active: string;
    role: string;
    partner_id?: string;
  };
  health_data: {
    journal_entries: Array<{
      id: string;
      text: string;
      mood?: string;
      created_at: string;
      is_shared: boolean;
      app_origin: string;
      sentiment_analysis?: any;
    }>;
    mood_tracking: Array<{
      date: string;
      mood: string;
      notes?: string;
      is_shared: boolean;
    }>;
  };
  privacy_settings: {
    consent_history: Array<{
      data_processing: boolean;
      sentiment_analysis: boolean;
      anonymized_licensing: boolean;
      research_participation: boolean;
      consent_timestamp: string;
      ip_address: string;
      jurisdiction: string;
      version: string;
    }>;
    partner_sharing: {
      has_partner: boolean;
      partner_connection_date?: string;
      shared_entries_count: number;
    };
  };
  usage_statistics: {
    total_journal_entries: number;
    total_mood_entries: number;
    account_age_days: number;
    last_export_date?: string;
  };
  audit_logs: Array<{
    action: string;
    timestamp: string;
    details?: any;
  }>;
}

interface UserDataManagerProps {
  userId: string;
  onExportComplete?: (exportData: UserDataExport) => void;
  onDeleteComplete?: () => void;
}

export const UserDataManager: React.FC<UserDataManagerProps> = ({
  userId,
  onExportComplete,
  onDeleteComplete
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState<string>('');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deleteConfirmationText, setDeleteConfirmationText] = useState('');

  // Export user data in compliance with GDPR Article 15 and PIPEDA
  const handleDataExport = useCallback(async () => {
    setIsExporting(true);
    setExportProgress(0);
    setExportStatus('Preparing data export...');

    try {
      // Get Firebase app instance
      const { db } = await import('@/lib/firebase');
      const { 
        collection, 
        query, 
        where, 
        getDocs, 
        doc, 
        getDoc,
        orderBy 
      } = await import('firebase/firestore');

      // Step 1: Get user information (10%)
      setExportProgress(10);
      setExportStatus('Collecting account information...');
      
      const userDoc = await getDoc(doc(db, 'users', userId));
      const userData = userDoc.data();

      // Step 2: Get consent history (20%)
      setExportProgress(20);
      setExportStatus('Collecting privacy settings...');
      
      const consentDoc = await getDoc(doc(db, 'user_consents', userId));
      const consentData = consentDoc.data() as UserConsent;

      // Step 3: Get all journal entries (40%)
      setExportProgress(40);
      setExportStatus('Collecting journal entries...');
      
      const journalQuery = query(
        collection(db, 'journal_entries'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      const journalSnapshot = await getDocs(journalQuery);
      const journalEntries = journalSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as JournalEntry[];

      // Step 4: Get audit logs (60%)
      setExportProgress(60);
      setExportStatus('Collecting activity logs...');
      
      const auditQuery = query(
        collection(db, 'audit_logs'),
        where('userId', '==', userId),
        orderBy('timestamp', 'desc')
      );
      const auditSnapshot = await getDocs(auditQuery);
      const auditLogs = auditSnapshot.docs.map(doc => doc.data());

      // Step 5: Process and structure data (80%)
      setExportProgress(80);
      setExportStatus('Structuring export data...');

      const exportData: UserDataExport = {
        export_info: {
          timestamp: new Date().toISOString(),
          user_id: userId,
          export_type: 'full_data_export',
          jurisdiction: consentData?.jurisdiction || 'US',
          version: '1.0'
        },
        personal_information: {
          user_id: userId,
          email: userData?.email || '',
          display_name: userData?.displayName || '',
          account_created: userData?.createdAt?.toDate?.()?.toISOString() || '',
          last_active: userData?.lastActiveAt?.toDate?.()?.toISOString() || '',
          role: userData?.role || 'primary',
          partner_id: userData?.partnerId || undefined
        },
        health_data: {
          journal_entries: journalEntries.map(entry => ({
            id: entry.id,
            text: entry.text,
            mood: entry.mood,
            created_at: entry.createdAt?.toDate?.()?.toISOString() || '',
            is_shared: entry.isShared || false,
            app_origin: entry.appOrigin || 'meno-wellness',
            sentiment_analysis: entry.analysis || undefined
          })),
          mood_tracking: journalEntries
            .filter(entry => entry.mood)
            .map(entry => ({
              date: entry.createdAt?.toDate?.()?.toISOString() || '',
              mood: entry.mood || '',
              notes: entry.text || '',
              is_shared: entry.isShared || false
            }))
        },
        privacy_settings: {
          consent_history: consentData ? [{
            data_processing: consentData.dataProcessing || false,
            sentiment_analysis: consentData.sentimentAnalysis || false,
            anonymized_licensing: consentData.anonymizedLicensing || false,
            research_participation: consentData.researchParticipation || false,
            consent_timestamp: consentData.consentTimestamp?.toDate?.()?.toISOString() || '',
            ip_address: consentData.ipAddress || '',
            jurisdiction: consentData.jurisdiction || 'US',
            version: consentData.version || '1.0'
          }] : [],
          partner_sharing: {
            has_partner: !!userData?.partnerId,
            partner_connection_date: userData?.partnerId ? userData?.createdAt?.toDate?.()?.toISOString() : undefined,
            shared_entries_count: journalEntries.filter(entry => entry.isShared).length
          }
        },
        usage_statistics: {
          total_journal_entries: journalEntries.filter(entry => entry.text).length,
          total_mood_entries: journalEntries.filter(entry => entry.mood).length,
          account_age_days: userData?.createdAt ? 
            Math.floor((Date.now() - userData.createdAt.toDate().getTime()) / (1000 * 60 * 60 * 24)) : 0,
          last_export_date: new Date().toISOString()
        },
        audit_logs: auditLogs.map(log => ({
          action: log.action || '',
          timestamp: log.timestamp?.toDate?.()?.toISOString() || '',
          details: log.details || undefined
        }))
      };

      // Step 6: Log export action (90%)
      setExportProgress(90);
      setExportStatus('Logging export action...');
      
      const { ComplianceUtils } = await import('./security-utils');
      const auditLog = ComplianceUtils.createAuditLog(
        userId,
        'data_export_requested',
        undefined,
        'user_data',
        { export_size: JSON.stringify(exportData).length }
      );

      const { addDoc } = await import('firebase/firestore');
      await addDoc(collection(db, 'audit_logs'), auditLog);

      // Step 7: Complete export (100%)
      setExportProgress(100);
      setExportStatus('Export complete!');

      // Trigger download
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `menowellness-data-export-${userId}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      onExportComplete?.(exportData);

      setTimeout(() => {
        setExportProgress(0);
        setExportStatus('');
      }, 3000);

    } catch (error) {
      console.error('Data export failed:', error);
      setExportStatus('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  }, [userId, onExportComplete]);

  // Delete user account and all associated data
  const handleAccountDeletion = useCallback(async () => {
    if (deleteConfirmationText !== 'DELETE MY ACCOUNT') {
      alert('Please type "DELETE MY ACCOUNT" to confirm deletion.');
      return;
    }

    setIsDeleting(true);

    try {
      // This will be implemented with a secure Cloud Function
      // For now, we'll create the request
      const { db } = await import('@/lib/firebase');
      const { addDoc, collection } = await import('firebase/firestore');

      // Create deletion request
      await addDoc(collection(db, 'deletion_requests'), {
        userId,
        requestedAt: new Date(),
        status: 'pending',
        confirmationText: deleteConfirmationText,
        ipAddress: await getClientIP(),
        userAgent: navigator.userAgent
      });

      // Log deletion request
      const { ComplianceUtils } = await import('./security-utils');
      const auditLog = ComplianceUtils.createAuditLog(
        userId,
        'account_deletion_requested',
        undefined,
        'user_account',
        { confirmation_provided: true }
      );

      await addDoc(collection(db, 'audit_logs'), auditLog);

      alert('Account deletion request submitted. Your account will be deleted within 30 days as required by law.');
      onDeleteComplete?.();

    } catch (error) {
      console.error('Account deletion request failed:', error);
      alert('Deletion request failed. Please contact support.');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirmation(false);
      setDeleteConfirmationText('');
    }
  }, [userId, deleteConfirmationText, onDeleteComplete]);

  const getClientIP = async (): Promise<string> => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return '0.0.0.0';
    }
  };

  return (
    <div className="space-y-6">
      {/* Data Export Section */}
      <div className="bg-white border border-blue-200 rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="text-2xl">📊</div>
          <h3 className="text-xl font-semibold text-gray-800">Export Your Data</h3>
        </div>
        
        <p className="text-gray-600 mb-6">
          Download all your personal data in a structured format. This includes your journal entries, 
          mood data, privacy settings, and account information in compliance with data protection laws.
        </p>

        {isExporting && (
          <div className="mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-blue-800 font-medium">{exportStatus}</span>
                <span className="text-blue-600 text-sm">{exportProgress}%</span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${exportProgress}%` }}
                />
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-4">
          <Button
            onClick={handleDataExport}
            disabled={isExporting}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 disabled:bg-gray-400 disabled:scale-100"
          >
            {isExporting ? (
              <div className="flex items-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Exporting...
              </div>
            ) : (
              '📊 Export My Data'
            )}
          </Button>
          
          <div className="text-sm text-gray-500">
            <div>• JSON format</div>
            <div>• GDPR & PIPEDA compliant</div>
            <div>• Includes all personal data</div>
          </div>
        </div>
      </div>

      {/* Account Deletion Section */}
      <div className="bg-red-50 border border-red-200 rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="text-2xl">🗑️</div>
          <h3 className="text-xl font-semibold text-gray-800">Delete Account</h3>
        </div>
        
        <div className="bg-red-100 border border-red-300 rounded-lg p-4 mb-6">
          <h4 className="font-semibold text-red-800 mb-2">⚠️ Permanent Action</h4>
          <p className="text-red-700 text-sm">
            This will permanently delete your account and all associated data including:
          </p>
          <ul className="text-red-700 text-sm mt-2 list-disc list-inside">
            <li>All journal entries and mood data</li>
            <li>Personal information and preferences</li>
            <li>Partner connections and shared data</li>
            <li>Privacy settings and consent history</li>
          </ul>
        </div>

        {!showDeleteConfirmation ? (
          <Button
            onClick={() => setShowDeleteConfirmation(true)}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
          >
            🗑️ Delete My Account
          </Button>
        ) : (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type "DELETE MY ACCOUNT" to confirm:
              </label>
              <input
                type="text"
                value={deleteConfirmationText}
                onChange={(e) => setDeleteConfirmationText(e.target.value)}
                placeholder="DELETE MY ACCOUNT"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              />
            </div>
            
            <div className="flex space-x-4">
              <Button
                onClick={handleAccountDeletion}
                disabled={isDeleting || deleteConfirmationText !== 'DELETE MY ACCOUNT'}
                className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 disabled:bg-gray-400 disabled:scale-100"
              >
                {isDeleting ? (
                  <div className="flex items-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Processing...
                  </div>
                ) : (
                  'Confirm Deletion'
                )}
              </Button>
              
              <Button
                onClick={() => {
                  setShowDeleteConfirmation(false);
                  setDeleteConfirmationText('');
                }}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};